using UnityEngine;
using System.Collections.Generic;
using FMODUnity;
using System;

/// <summary>
/// Represents a flight time boost source with metadata
/// </summary>
[Serializable]
public class FlightBoostSource
{
    /// <summary>
    /// The GameObject that is the source of this boost
    /// </summary>
    public GameObject sourceObject;

    /// <summary>
    /// The amount of flight time boost provided
    /// </summary>
    public float boostAmount;

    /// <summary>
    /// Optional color for visual feedback
    /// </summary>
    public Color boostColor = Color.white;

    /// <summary>
    /// Optional tag for categorizing boost sources
    /// </summary>
    public string boostTag = "";

    /// <summary>
    /// Time when this boost was last updated
    /// </summary>
    public float lastUpdateTime;

    /// <summary>
    /// Visual feedback objects associated with this boost
    /// </summary>
    public GameObject visualFeedback;
}

/// <summary>
/// Controls player movement, gravity, and interactions with SoulCreatures.
/// </summary>
public class PlayerController : MonoBehaviour
{
    [Header("Player Settings")]
    [Tooltip("Base movement speed.")]
    public float moveSpeed = 4f;
    [Toolt<PERSON>("Base fly speed.")]
    public float flySpeed = 2f;
    [Header("Flight Boost Settings")]
    [Tooltip("Maximum total boost time the player can accumulate.")]
    public float maxTotalBoostCapacity = 20f;
    [Tooltip("Maximum number of boost sources to track")]
    public int maxBoostSources = 10;

    [Header("Flight Boost Debug")]
    [Tooltip("Enable debug logging for flight boosts")]
    public bool debugFlightBoosts = false;

    [Header("Gravity Settings")]
    [Tooltip("When enabled, the player is not affected by downward gravity")]
    public bool disableGravity = false;

    // --- Internal boost tracking ---
    private float currentTotalActualBoostTime = 0f;
    private Dictionary<GameObject, FlightBoostSource> flightBoostSources = new Dictionary<GameObject, FlightBoostSource>();

    [Header("References")]
    private Transform cameraTransform; // Now private, will get from GameManager
    public GameObject oceanBubbles;
    public GameObject passiveOceanBubbles;

    [Header("Jetfuel Particles")]
    [SerializeField] private ParticleSystem jetfuel;
    [SerializeField] private ParticleSystem jetfuel2_bubbles;
    [Header("Backpack Particles")]
    [SerializeField] private GameObject backpackParticles;
    [Header("Backpack Halo Particles")]
    [SerializeField] private GameObject backpackParticlesHalo;
    [Tooltip("Reference to the BackpackParticlesPool (optional)")]
    private BackpackParticlesPool particlesPool;
    [Header("Ocean Exit Bubbles")]
    [SerializeField] private GameObject oceanExitBubbles; // Assign your exit bubbles prefab

    // --- SoulCreature interaction system ---
    private class SoulCreatureBoost
    {
        public GameObject sourceCreature; // Reference to the creature that gave this boost
        public float currentFlightContribution = 0f; // How much this creature is currently contributing to flight time
        public float maxPossibleFlightContribution = 0f; // The max this creature CAN contribute (its flightBoostValue)
        public float currentSpeedContribution = 0f; // How much this creature is currently contributing to speed boost
        public float maxPossibleSpeedContribution = 0f; // The max this creature CAN contribute (its speedBoostValue)

        public GameObject backpackParticlesInstance = null;
        public ParticleSystem psInstance = null;
        public GameObject backpackParticlesHaloInstance = null;
        public ParticleSystem psHaloInstance = null;

        public bool pendingDestruction = false;
        public float destructionTimer = 0f;
        public bool haloPendingDestruction = false;
        public float haloDestructionTimer = 0f;
    }
    // map SoulCreature GameObject to SoulCreatureBoost object
    private Dictionary<GameObject, SoulCreatureBoost> soulCreatureBoosts = new();

    private ParticleSystem.MainModule jetfuelMain;
    private ParticleSystem.EmissionModule jetfuelEmission;
    private ParticleSystem.MainModule jetfuel2Main;
    private ParticleSystem.EmissionModule jetfuel2Emission;

    private CharacterController controller;
    private Vector3 velocity;
    private float flyTimer = 0f;
    private float maxFlyTime = 0.5f;

    [HideInInspector]
    public Vector3 lastMoveDirectionXZ = Vector3.zero;
    [HideInInspector]
    public Vector3 lastMoveDirection3D = Vector3.zero;

    // Environment state
    public bool isInsideOcean = false;
    public float additionalMaxFlyTime = 0.0f;

    // --- Refactored: Use EnvironmentManager for gravity/maxFlyTime ---
    private EnvironmentManager envManager;

    // Add missing fields for ocean bottom Y and ocean bubbles trigger
    private float oceanBottomY = 0.0f;
    private bool shouldOceanBubbles = false;

    private float lastY = 0f; // Track previous Y for ocean entry/exit detection
    private bool wasGroundedLastFrame = false; // Track grounded state changes

    // --- New: Accumulate external movement (e.g., from river) ---
    [HideInInspector]
    public Vector3 externalMovementThisFrame = Vector3.zero;

    // --- Speed Magnetism System ---
    private float magnetismUpdateTimer = 0f;
    private float currentPlayerSpeed = 0f;
    private float cachedMagnetismMaxSpeed = 0f; // Calculated at startup based on player's actual max achievable speed
    private List<CreatureMovement> affectedCreatures = new List<CreatureMovement>();

    // --- Static Max Speed (accessible by other systems like camera) ---
    public static float StaticMaxPlayerSpeed { get; private set; } = 0f;

    // --- Speed Boost System (Simplified) ---
    [Header("Speed Boost System")]
    [Tooltip("Current accumulated speed boost from soul creatures.")]
    public float currentSpeedBoost = 0f;

    [Header("Speed Boost Settings")]
    [Tooltip("Maximum speed boost that can be accumulated.")]
    public float maxSpeedBoost = 10f;
    [Tooltip("Rate at which speed boost decays per second.")]
    public float speedBoostDecayRate = 1.0f;

    [Header("Speed Boost Multipliers")]
    [Tooltip("How much of the speed boost applies to horizontal movement.")]
    public float horizontalSpeedMultiplier = 1.0f;
    [Tooltip("How much of the speed boost applies to vertical fly speed.")]
    public float verticalSpeedMultiplier = 0.5f;

    [Header("Directional Movement Multipliers")]
    [Tooltip("Multiplier for forward movement (W key). 1.0 = full boost applied.")]
    [Range(0f, 1f)]
    public float forwardSpeedMultiplier = 1.0f;
    [Tooltip("Multiplier for diagonal movement (W+A or W+D). Should be less than forward.")]
    [Range(0f, 1f)]
    public float diagonalSpeedMultiplier = 0.7f;
    [Tooltip("Multiplier for pure sideways movement (A or D only). Should be much less.")]
    [Range(0f, 1f)]
    public float sidewaysSpeedMultiplier = 0.3f;

    [Header("Speed Magnetism System")]
    [Tooltip("Maximum distance from player at which creatures are affected by speed magnetism")]
    public float magnetismRadius = 15f;
    [Tooltip("How often to check and update affected creatures (seconds)")]
    public float magnetismUpdateInterval = 0.1f;
    [Tooltip("Multiplier applied to calculated max speed for magnetism scaling (1.0 = normal, >1.0 = more sensitive)")]
    [Range(0.5f, 2.0f)] public float magnetismSensitivity = 1.2f;

    [Header("Speed Magnetism Multipliers")]
    [Tooltip("How much to reduce waypoint wandering weight during magnetism (0=no reduction, 1=complete reduction)")]
    [Range(0f, 1f)] public float waypointReductionMultiplier = 0.8f;
    [Tooltip("How strongly player speed affects player mirroring weight")]
    [Range(0f, 5f)] public float speedMirroringMultiplier = 1.5f;
    [Tooltip("How strongly player speed affects player attraction weight")]
    [Range(0f, 5f)] public float speedAttractionMultiplier = 2.0f;
    [Tooltip("How strongly player speed affects dolphin lunge weight")]
    [Range(0f, 5f)] public float speedLungeMultiplier = 1.0f;
    [Tooltip("How strongly player speed affects flocking weight")]
    [Range(0f, 5f)] public float speedFlockingMultiplier = 1.2f;
    [Tooltip("How strongly player speed affects orbiting weight")]
    [Range(0f, 5f)] public float speedOrbitingMultiplier = 0.8f;

    [Header("Speed Magnetism Debug")]
    [Tooltip("Enable debug logging for magnetism system")]
    public bool debugMagnetism = false;

    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;

    // --- Reusable collections to avoid garbage allocation ---
    private List<GameObject> reusableKeysToRemove = new List<GameObject>();
    private List<GameObject> reusableKeysSnapshot = new List<GameObject>();
    private List<GameObject> reusableFlightBoostSourcesToRemove = new List<GameObject>();

    void Start()
    {
        controller = GetComponent<CharacterController>();
        envManager = EnvironmentManager.Instance;

        // Get camera reference from GameManager
        if (GameManager.Instance != null && GameManager.Instance.followCamera != null)
        {
            cameraTransform = GameManager.Instance.followCamera.transform;
        }
        else
        {
            Debug.LogError("FollowCamera reference not found in GameManager!");
        }

        // Initialize oceanBottomY from EnvironmentManager if possible
        if (envManager != null)
            oceanBottomY = envManager.oceanBottomY;

        shouldOceanBubbles = true;

        if (jetfuel != null)
        {
            jetfuelMain = jetfuel.main;
            jetfuelEmission = jetfuel.emission;
            jetfuelEmission.rateOverTime = 0f; // Start with no emission
        }
        if (jetfuel2_bubbles != null)
        {
            jetfuel2Main = jetfuel2_bubbles.main;
            jetfuel2Emission = jetfuel2_bubbles.emission;
            jetfuel2Emission.rateOverTime = 0f; // Start with no emission
        }

        lastY = transform.position.y;

        // Initialize magnetism system with calculated max speed
        InitializeMagnetismSystem();
    }

    void Update()
    {
        HandleSoulCreatureBoosts();
        HandleFlightBoosts();
        HandleMovement();
        UpdateSpeedMagnetism();
        UpdateJetfuelParticles();

        // Lock the player's rotation
        transform.rotation = Quaternion.Euler(0, 0, 0);

        // --- Use EnvironmentManager for environment state ---
        var env = envManager.GetEnvironmentState(transform.position.y);
        isInsideOcean = env == EnvironmentType.Ocean;

        // Ocean bubbles and passive bubbles logic
        float posY = transform.position.y;

        // --- Ocean bubbles logic ---
        // Detect entry/exit through bottom
        bool wasInsideOcean = (lastY >= oceanBottomY && lastY < envManager.cloudsBottomY);
        bool nowInsideOcean = (posY >= oceanBottomY && posY < envManager.cloudsBottomY);

        // Entering ocean from below
        if (!wasInsideOcean && nowInsideOcean && lastY < oceanBottomY && posY >= oceanBottomY)
        {
            passiveOceanBubbles.SetActive(true);
            if (shouldOceanBubbles)
            {
                var o = Instantiate(oceanBubbles, new Vector3(transform.position.x, oceanBottomY, transform.position.z), Quaternion.identity);
                shouldOceanBubbles = false;
                Destroy(o, 10f);
            }
        }
        // Exiting ocean through bottom
        else if (wasInsideOcean && !nowInsideOcean && lastY >= oceanBottomY && posY < oceanBottomY)
        {
            passiveOceanBubbles.SetActive(false);
            shouldOceanBubbles = true;
            if (oceanExitBubbles != null)
            {
                var o = Instantiate(oceanExitBubbles, new Vector3(transform.position.x, oceanBottomY, transform.position.z), Quaternion.identity);
                Destroy(o, 10f);
            }
        }
        // Entering/exiting at the top (clouds boundary): do nothing
        else if (posY >= envManager.cloudsBottomY)
        {
            passiveOceanBubbles.SetActive(false);
        }

        lastY = posY;

        // Update grounded state tracking
        wasGroundedLastFrame = controller.isGrounded;
    }

    void HandleMovement()
    {
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);

        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");

        Vector3 camForward = cameraTransform.forward;
        Vector3 camRight = cameraTransform.right;

        camForward.y = 0;
        camRight.y = 0;

        camForward.Normalize();
        camRight.Normalize();

        Vector3 move = camForward * vertical + camRight * horizontal;

        // Calculate directional speed modifier based on input
        float speedMultiplier = CalculateDirectionalSpeedMultiplier(horizontal, vertical);

        // Apply speed boost with directional and horizontal multipliers
        float horizontalSpeedBoost = currentSpeedBoost * horizontalSpeedMultiplier * speedMultiplier;
        float effectiveMoveSpeed = moveSpeed + horizontalSpeedBoost;

        lastMoveDirectionXZ = move * effectiveMoveSpeed;
        lastMoveDirection3D = lastMoveDirectionXZ;

        controller.Move(move * effectiveMoveSpeed * clampedDeltaTime);

        // Use environment-dependent gravity and maxFlyTime
        var env = envManager.GetEnvironmentState(transform.position.y);
        float gravity = envManager.GetGravity(env);

        // Infinite fly time above ocean bottom
        bool infiniteFlyTime = env != EnvironmentType.BelowOcean;
        float effectiveMaxFlyTime = infiniteFlyTime ? float.MaxValue : maxFlyTime + additionalMaxFlyTime;

        if (controller.isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
            flyTimer = 0f;
        }
        // Fallback: Reset flyTimer if player is very close to ground level (y <= -0.4) and falling
        else if (transform.position.y <= -0.4f && velocity.y <= 0 && flyTimer > 0)
        {
            flyTimer = 0f;
        }
        else
        {
            if (disableGravity)
            {
                // When gravity is disabled:
                // 1. Apply positive gravity (if any) to allow upward environmental forces
                // 2. Apply a small dampening effect to prevent drift when not actively flying
                if (gravity > 0)
                {
                    velocity.y += gravity * clampedDeltaTime;
                }
                else if (!Input.GetKey(KeyCode.Space) && Mathf.Abs(velocity.y) < 0.1f)
                {
                    // Apply a small dampening effect to stabilize hovering
                    velocity.y = 0f;
                }
            }
            else
            {
                // Normal gravity behavior when gravity is enabled
                velocity.y += gravity * clampedDeltaTime;
            }
        }

        // Reset flyTimer to maxFlyTime when using additional flight time
        // This ensures we're using the additional time first
        if (!infiniteFlyTime && flyTimer > maxFlyTime && additionalMaxFlyTime > 0)
        {
            flyTimer = maxFlyTime;
        }

        // Apply flying when space is pressed and we have flight time available
        if (Input.GetKey(KeyCode.Space) && (infiniteFlyTime || flyTimer < effectiveMaxFlyTime))
        {
            // Apply upward velocity with vertical speed boost
            float verticalSpeedBoost = currentSpeedBoost * verticalSpeedMultiplier;
            float effectiveFlySpeed = flySpeed + verticalSpeedBoost;
            velocity.y = effectiveFlySpeed;

            // Increment the flight timer only if not infinite
            if (!infiniteFlyTime)
                flyTimer += clampedDeltaTime;

            // Update the movement direction for visuals
            lastMoveDirection3D.y = effectiveFlySpeed;
        }
        else if (!Input.GetKeyDown(KeyCode.Space))
        {
            // If gravity is disabled and player just released space, gradually reduce vertical movement
            if (disableGravity && Input.GetKeyUp(KeyCode.Space))
            {
                // Apply a dampening effect to create a smoother transition to hovering
                velocity.y *= 0.3f; // Reduce velocity by 70% immediately when releasing space
            }

            // Apply additional dampening when gravity is disabled and not pressing space
            if (disableGravity && !Input.GetKey(KeyCode.Space))
            {
                // Apply continuous dampening to stabilize hovering
                velocity.y *= 0.8f; // Small continuous dampening
            }

            if (transform.position.y > 0.5f)
                lastMoveDirection3D.y = velocity.y;
        }
        controller.Move(velocity * clampedDeltaTime);

        // --- Apply external movement (e.g., from river) ---
        if (externalMovementThisFrame.sqrMagnitude > 0.00001f)
        {
            controller.Move(externalMovementThisFrame);
            externalMovementThisFrame = Vector3.zero;
        }
    }

    /// <summary>
    /// Calculates the speed multiplier based on movement direction
    /// </summary>
    /// <param name="horizontal">Horizontal input (-1 to 1)</param>
    /// <param name="vertical">Vertical input (-1 to 1)</param>
    /// <returns>Speed multiplier for the current movement direction</returns>
    private float CalculateDirectionalSpeedMultiplier(float horizontal, float vertical)
    {
        // If no movement input, return 0
        if (Mathf.Abs(horizontal) < 0.01f && Mathf.Abs(vertical) < 0.01f)
            return 0f;

        // Pure forward movement (W key only)
        if (Mathf.Abs(horizontal) < 0.01f && vertical > 0.01f)
            return forwardSpeedMultiplier;

        // Pure sideways movement (A or D key only)
        if (Mathf.Abs(vertical) < 0.01f && Mathf.Abs(horizontal) > 0.01f)
            return sidewaysSpeedMultiplier;

        // Diagonal movement (W+A or W+D)
        if (vertical > 0.01f && Mathf.Abs(horizontal) > 0.01f)
            return diagonalSpeedMultiplier;

        // Backward or other movements get minimal boost
        return sidewaysSpeedMultiplier;
    }

    /// <summary>
    /// Initializes the magnetism system by calculating the realistic maximum speed the player can achieve
    /// </summary>
    void InitializeMagnetismSystem()
    {
        // Calculate the realistic maximum speed the player can achieve
        // This includes base speed + maximum possible speed boosts + some buffer for external forces
        float baseMaxSpeed = moveSpeed + maxSpeedBoost;

        // Set static max speed for other systems (like camera FOV)
        StaticMaxPlayerSpeed = baseMaxSpeed;

        // Apply sensitivity multiplier to make the system more or less responsive
        cachedMagnetismMaxSpeed = baseMaxSpeed * magnetismSensitivity;

        if (debugMagnetism)
        {
            Debug.Log($"[Magnetism] Initialized - Base Max Speed: {baseMaxSpeed:F2}, Static Max Speed: {StaticMaxPlayerSpeed:F2}, Cached Magnetism Max Speed: {cachedMagnetismMaxSpeed:F2}, Sensitivity: {magnetismSensitivity:F2}");
        }
    }

    /// <summary>
    /// Recalculates the magnetism max speed if player parameters change during gameplay
    /// Call this if moveSpeed or maxSpeedBoost are modified at runtime
    /// </summary>
    public void RecalculateMagnetismMaxSpeed()
    {
        InitializeMagnetismSystem();
    }

    /// <summary>
    /// Gets the current player speed (for use by other systems like camera)
    /// </summary>
    public float GetCurrentPlayerSpeed()
    {
        return lastMoveDirectionXZ.magnitude;
    }

    /// <summary>
    /// Updates the speed magnetism system that affects nearby soul creatures based on player speed
    /// </summary>
    void UpdateSpeedMagnetism()
    {
        // Update timer
        magnetismUpdateTimer += Time.deltaTime;

        // Only update at specified intervals for performance
        if (magnetismUpdateTimer < magnetismUpdateInterval)
            return;

        magnetismUpdateTimer = 0f;

        // Calculate current player speed (actual speed from all sources)
        currentPlayerSpeed = lastMoveDirectionXZ.magnitude;

        // Use the cached maximum speed calculated at startup
        // This is based on the player's actual achievable maximum speed
        float maxSpeed = cachedMagnetismMaxSpeed;

        // Calculate speed factor (0 to 1, where 1 is realistic max speed)
        float speedFactor = maxSpeed > 0f ? Mathf.Clamp01(currentPlayerSpeed / maxSpeed) : 0f;

        if (debugMagnetism)
        {
            Debug.Log($"[Magnetism] Player Speed: {currentPlayerSpeed:F2}, Max Speed: {maxSpeed:F2}, Speed Factor: {speedFactor:F2}");
        }

        // Store previous affected creatures to reset those no longer in range
        var previouslyAffected = new List<CreatureMovement>(affectedCreatures);
        affectedCreatures.Clear();

        // Find creatures within magnetism radius using both registries
        // Check SoulCreatureLogic instances
        var allCreatures = SoulCreatureLogic.GetAllActiveInstances();
        foreach (var creature in allCreatures)
        {
            if (creature == null || creature.transform == null) continue;

            float distance = Vector3.Distance(transform.position, creature.transform.position);
            if (distance <= magnetismRadius)
            {
                // Calculate distance-based falloff (1.0 at player position, 0.0 at radius edge)
                float distanceFactor = 1f - (distance / magnetismRadius);

                // Get the CreatureMovement component
                CreatureMovement creatureMovement = creature.GetComponent<CreatureMovement>();
                if (creatureMovement != null)
                {
                    // Apply magnetism effect
                    ApplyMagnetismToCreature(creatureMovement, speedFactor, distanceFactor);
                    affectedCreatures.Add(creatureMovement);

                    if (debugMagnetism)
                    {
                        Debug.Log($"[Magnetism] Applied to {creature.name}: Distance={distance:F2}, DistanceFactor={distanceFactor:F2}, MagnetismStrength={speedFactor * distanceFactor:F2}");
                    }
                }
            }
        }

        // Check SoulCreatureGiant instances
        var allGiantCreatures = SoulCreatureGiant.GetAllActiveInstances();
        foreach (var creature in allGiantCreatures)
        {
            if (creature == null || creature.transform == null) continue;

            float distance = Vector3.Distance(transform.position, creature.transform.position);
            if (distance <= magnetismRadius)
            {
                // Calculate distance-based falloff (1.0 at player position, 0.0 at radius edge)
                float distanceFactor = 1f - (distance / magnetismRadius);

                // Get the CreatureMovement component
                CreatureMovement creatureMovement = creature.GetComponent<CreatureMovement>();
                if (creatureMovement != null)
                {
                    // Apply magnetism effect
                    ApplyMagnetismToCreature(creatureMovement, speedFactor, distanceFactor);
                    affectedCreatures.Add(creatureMovement);

                    if (debugMagnetism)
                    {
                        Debug.Log($"[Magnetism] Applied to Giant {creature.name}: Distance={distance:F2}, DistanceFactor={distanceFactor:F2}, MagnetismStrength={speedFactor * distanceFactor:F2}");
                    }
                }
            }
        }

        // Reset weights for creatures that are no longer affected
        foreach (var creatureMovement in previouslyAffected)
        {
            if (creatureMovement != null && !affectedCreatures.Contains(creatureMovement))
            {
                creatureMovement.ResetToBaseWeights();
            }
        }
    }

    /// <summary>
    /// Applies magnetism effects to a specific creature based on player speed and distance
    /// </summary>
    /// <param name="creatureMovement">The creature to affect</param>
    /// <param name="speedFactor">Player speed factor (0-1)</param>
    /// <param name="distanceFactor">Distance falloff factor (0-1)</param>
    void ApplyMagnetismToCreature(CreatureMovement creatureMovement, float speedFactor, float distanceFactor)
    {
        // Calculate final magnetism strength
        float magnetismStrength = speedFactor * distanceFactor;

        // Apply magnetism effects to movement weights
        creatureMovement.ApplySpeedMagnetism(
            magnetismStrength,
            waypointReductionMultiplier,
            speedMirroringMultiplier,
            speedAttractionMultiplier,
            speedLungeMultiplier,
            speedFlockingMultiplier,
            speedOrbitingMultiplier
        );
    }

    // --- Jetfuel particles ---
    void UpdateJetfuelParticles()
    {
        // Get environment state and max fly time
        var env = envManager.GetEnvironmentState(transform.position.y);
        float baseMaxFlyTime = env == EnvironmentType.BelowOcean ? 0.5f : float.MaxValue;
        float effectiveMaxFlyTime = baseMaxFlyTime + additionalMaxFlyTime;

        // Calculate fuel percentages (based on their respective timers)
        float baseFuelPercentage = (baseMaxFlyTime > 0 && baseMaxFlyTime < float.MaxValue) ? 1f - (flyTimer / baseMaxFlyTime) : 1f;
        float effectiveFuelPercentage = (effectiveMaxFlyTime > 0 && effectiveMaxFlyTime < float.MaxValue) ? 1f - (flyTimer / effectiveMaxFlyTime) : 1f;

        // --- Jetfuel (Standard) Particles ---
        if (jetfuel != null)
        {
            // Condition: Holding Space AND flyTimer < baseMaxFlyTime AND Player Y < oceanBottomY
            bool isStandardFlyingBelowOcean = Input.GetKey(KeyCode.Space) && flyTimer < baseMaxFlyTime && env == EnvironmentType.BelowOcean;

            if (isStandardFlyingBelowOcean)
            {
                jetfuelEmission.rateOverTime = Mathf.Lerp(5f, 25f, baseFuelPercentage);

                // Link color/lifetime to base fuel
                Color currentColor = jetfuelMain.startColor.color; // Get base color
                currentColor.a = Mathf.Lerp(0.1f, 0.2f, baseFuelPercentage);
                jetfuelMain.startColor = currentColor;
                jetfuelMain.startLifetime = Mathf.Lerp(0.3f, 1.0f, baseFuelPercentage);
            }
            else
            {
                jetfuelEmission.rateOverTime = 0f;
            }
        }

        // --- Jetfuel2_Bubbles Particles ---
        if (jetfuel2_bubbles != null)
        {
            // Condition: Holding Space AND flyTimer < effectiveMaxFlyTime AND isInsideOcean
            bool isAnyFlyingInsideOcean = Input.GetKey(KeyCode.Space) && flyTimer < effectiveMaxFlyTime && isInsideOcean;

            if (isAnyFlyingInsideOcean)
            {
                jetfuel2Emission.rateOverTime = Mathf.Lerp(5f, 25f, effectiveFuelPercentage);

                // Link color/lifetime to effective fuel
                Color currentColor = jetfuel2Main.startColor.color; // Get base color
                currentColor.a = 0.4f;
                jetfuel2Main.startColor = currentColor;
                jetfuel2Main.startLifetime = Mathf.Lerp(0.5f, 1.3f, effectiveFuelPercentage);
            }
            else
            {
                jetfuel2Emission.rateOverTime = 0f;
            }
        }
    }
    void HandleSoulCreatureBoosts()
    {
        // Check if we have any SoulCreature boosts
        if (soulCreatureBoosts.Count == 0 && currentTotalActualBoostTime <= 0)
        {
            // Don't reset additionalMaxFlyTime here, as it might come from external sources
            // We'll calculate the total at the end of this method
            return;
        }

        // Clear and reuse existing lists to avoid allocation
        reusableKeysToRemove.Clear();
        reusableKeysSnapshot.Clear();

        // --- Step 1: Identify null creatures for removal ---
        // Copy keys to reusable list to avoid issues if 'other' (the key) gets destroyed
        reusableKeysSnapshot.AddRange(soulCreatureBoosts.Keys);

        foreach (var key in reusableKeysSnapshot) // Use the snapshot
        {
            if (key == null) // The SoulCreature GameObject itself was destroyed
            {
                // Attempt to clean up particles for the orphaned boost entry
                if (soulCreatureBoosts.TryGetValue(key, out SoulCreatureBoost orphanedBoost))
                {
                    if (orphanedBoost.backpackParticlesInstance != null) Destroy(orphanedBoost.backpackParticlesInstance);
                    if (orphanedBoost.backpackParticlesHaloInstance != null) Destroy(orphanedBoost.backpackParticlesHaloInstance);
                }
                reusableKeysToRemove.Add(key); // Add null key to the list to be removed from dictionary
            }
        }

        // Remove null keys identified in Step 1 immediately
        foreach (var key in reusableKeysToRemove)
        {
            soulCreatureBoosts.Remove(key);
        }
        reusableKeysToRemove.Clear(); // Clear the list to reuse it for decay/zero-contribution checks

        // Re-create snapshot after removing null keys in Step 1
        reusableKeysSnapshot.Clear();
        reusableKeysSnapshot.AddRange(soulCreatureBoosts.Keys);


        // --- Step 2: Apply Fixed Decay Per Creature and identify entries with null source ---
        float individualDecayRate = 1.0f; // Adjust this rate as needed (decay units per second per creature)

        foreach (var key in reusableKeysSnapshot) // Use the snapshot from Step 1
        {
            // Check if key or source creature is null (might happen if creature was destroyed between snapshots)
            if (key == null || !soulCreatureBoosts.TryGetValue(key, out var boost) || boost.sourceCreature == null)
            {
                if (soulCreatureBoosts.ContainsKey(key)) reusableKeysToRemove.Add(key); // Add to reusableKeysToRemove list
                continue;
            }

            // Apply fixed decay to both flight and speed contributions
            if (boost.currentFlightContribution > 0f)
            {
                float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
                boost.currentFlightContribution -= individualDecayRate * clampedDeltaTime;
                boost.currentFlightContribution = Mathf.Max(0f, boost.currentFlightContribution); // Clamp at zero
            }
            if (boost.currentSpeedContribution > 0f)
            {
                float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
                boost.currentSpeedContribution -= individualDecayRate * clampedDeltaTime;
                boost.currentSpeedContribution = Mathf.Max(0f, boost.currentSpeedContribution); // Clamp at zero
            }
        }
        // reusableKeysToRemove now contains entries where the source creature was destroyed.
        // This list will be used in the final removal step.


        // --- Step 3: Recalculate totals, manage particles, and flag for removal if contribution is zero ---
        currentTotalActualBoostTime = 0f;
        currentSpeedBoost = 0f;
        // Use the reusableKeysSnapshot from Step 1 for this loop as well

        foreach (var key in reusableKeysSnapshot)
        {
            // Check validity again, might have been added to keysToRemove in Step 2
            if (key == null || !soulCreatureBoosts.TryGetValue(key, out var boost) || boost.sourceCreature == null)
            {
                // If already marked in Step 2, it's in keysToRemove. Skip.
                continue;
            }

            currentTotalActualBoostTime += boost.currentFlightContribution;
            currentSpeedBoost += boost.currentSpeedContribution;

            // Calculate ratios for particle effects (consider both flight and speed contributions)
            float flightRatio = boost.maxPossibleFlightContribution > 0 ?
                               boost.currentFlightContribution / boost.maxPossibleFlightContribution : 0f;
            float speedRatio = boost.maxPossibleSpeedContribution > 0 ?
                              boost.currentSpeedContribution / boost.maxPossibleSpeedContribution : 0f;

            // Use the maximum of both ratios for particle emission (so either boost type can drive particles)
            float individualBoostRatio = Mathf.Max(flightRatio, speedRatio);

            // BackpackParticles
            if (boost.backpackParticlesInstance != null && boost.psInstance != null)
            {
                float emissionRate = 0f;
                if (!boost.pendingDestruction)
                {
                    emissionRate = Mathf.Lerp(0f, 10f, individualBoostRatio);
                }

                // Use the pool to update emission rate if available
                if (particlesPool != null)
                {
                    particlesPool.UpdateEmissionRate(boost.backpackParticlesInstance, emissionRate);
                }
                else
                {
                    var emission = boost.psInstance.emission;
                    emission.rateOverTime = emissionRate;
                }

                if (boost.currentFlightContribution <= 0f && boost.currentSpeedContribution <= 0f && !boost.pendingDestruction)
                {
                    boost.pendingDestruction = true;
                    boost.destructionTimer = boost.psInstance.main.startLifetime.constantMax;

                    // Set emission rate to 0
                    if (particlesPool != null)
                    {
                        particlesPool.UpdateEmissionRate(boost.backpackParticlesInstance, 0f);
                    }
                    else
                    {
                        var emission = boost.psInstance.emission;
                        emission.rateOverTime = 0f;
                    }
                }
                else if (boost.pendingDestruction)
                {
                    float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
                    boost.destructionTimer -= clampedDeltaTime;
                    if (boost.destructionTimer <= 0f)
                    {
                        // Release to pool if available
                        if (particlesPool != null)
                        {
                            particlesPool.ReleaseParticleSystem(boost.backpackParticlesInstance);
                        }
                        else
                        {
                            Destroy(boost.backpackParticlesInstance);
                        }
                        boost.backpackParticlesInstance = null;
                        boost.psInstance = null;
                    }
                }
            }

            // BackpackParticlesHalo
            if (boost.backpackParticlesHaloInstance != null && boost.psHaloInstance != null)
            {
                float haloEmissionRate = 0f;
                if (!boost.haloPendingDestruction)
                {
                    haloEmissionRate = Mathf.Lerp(0f, 10f, individualBoostRatio);
                }

                // Use the pool to update emission rate if available
                if (particlesPool != null)
                {
                    particlesPool.UpdateEmissionRate(boost.backpackParticlesHaloInstance, haloEmissionRate);
                }
                else
                {
                    var haloEmission = boost.psHaloInstance.emission;
                    haloEmission.rateOverTime = haloEmissionRate;
                }

                if (boost.currentFlightContribution <= 0f && boost.currentSpeedContribution <= 0f && !boost.haloPendingDestruction)
                {
                    boost.haloPendingDestruction = true;
                    boost.haloDestructionTimer = boost.psHaloInstance.main.startLifetime.constantMax;

                    // Set emission rate to 0
                    if (particlesPool != null)
                    {
                        particlesPool.UpdateEmissionRate(boost.backpackParticlesHaloInstance, 0f);
                    }
                    else
                    {
                        var haloEmission = boost.psHaloInstance.emission;
                        haloEmission.rateOverTime = 0f;
                    }
                }
                else if (boost.haloPendingDestruction)
                {
                    boost.haloDestructionTimer -= Time.deltaTime;
                    if (boost.haloDestructionTimer <= 0f)
                    {
                        // Release to pool if available
                        if (particlesPool != null)
                        {
                            particlesPool.ReleaseParticleSystem(boost.backpackParticlesHaloInstance);
                        }
                        else
                        {
                            Destroy(boost.backpackParticlesHaloInstance);
                        }
                        boost.backpackParticlesHaloInstance = null;
                        boost.psHaloInstance = null;
                    }
                }
            }

            // If both contributions are zero and particles are destroyed, mark for removal
            if (boost.currentFlightContribution <= 0f && boost.currentSpeedContribution <= 0f &&
                boost.backpackParticlesInstance == null &&
                boost.backpackParticlesHaloInstance == null)
            {
                // Ensure it's not already marked for removal
                if (!reusableKeysToRemove.Contains(key))
                {
                    reusableKeysToRemove.Add(key);
                }
            }
        }

        // Calculate total external boost
        float totalExternalBoost = 0f;
        foreach (var boost in flightBoostSources.Values)
        {
            totalExternalBoost += boost.boostAmount;
        }

        // Combine SoulCreature boosts with external boosts
        additionalMaxFlyTime = Mathf.Max(0f, currentTotalActualBoostTime + totalExternalBoost);

        // Speed boost is now calculated directly from creature contributions above
        // Clamp to max speed boost
        currentSpeedBoost = Mathf.Min(currentSpeedBoost, maxSpeedBoost);

        // --- Final Step: Remove entries marked for removal ---
        foreach (var keyToRemove in reusableKeysToRemove)
        {
            if (soulCreatureBoosts.ContainsKey(keyToRemove))
            {
                // Optional: Clean up particles one last time just in case
                if (soulCreatureBoosts.TryGetValue(keyToRemove, out SoulCreatureBoost boostToRemove))
                {
                    if (boostToRemove.backpackParticlesInstance != null) Destroy(boostToRemove.backpackParticlesInstance);
                    if (boostToRemove.backpackParticlesHaloInstance != null) Destroy(boostToRemove.backpackParticlesHaloInstance);
                }
                soulCreatureBoosts.Remove(keyToRemove);
            }
        }
    }
    // --- SoulCreature particle collision handler ---
    void OnParticleCollision(GameObject other)
    {
        if (!other.CompareTag("SoulCreature")) return;

        // Get speed boost value (for all creatures)
        float speedBoostValue = 0.0f;
        var boostProvider = other.GetComponent<ISoulCreatureBoostProvider>();
        if (boostProvider != null)
            speedBoostValue = boostProvider.GetSpeedBoostValue();

        // Get flight time boost value (for all soul creatures, not just tutorial)
        float creatureBoostValue = 6.0f;

        // Skip if no boosts available
        if (speedBoostValue <= 0f && creatureBoostValue <= 0f) return;

        SoulCreatureBoost boostEntry;

        if (!soulCreatureBoosts.TryGetValue(other, out boostEntry))
        {
            boostEntry = new SoulCreatureBoost();
            boostEntry.sourceCreature = other;
            boostEntry.maxPossibleFlightContribution = creatureBoostValue;
            boostEntry.currentFlightContribution = 0f;
            boostEntry.maxPossibleSpeedContribution = speedBoostValue;
            boostEntry.currentSpeedContribution = 0f;
            soulCreatureBoosts[other] = boostEntry;

            // Get the color from the soul creature's particle system
            Color soulColor = Color.white;
            var soulPs = other.GetComponentInChildren<ParticleSystem>();
            if (soulPs != null)
            {
                soulColor = soulPs.main.startColor.color;
            }

            // Get the sound event from the soul creature
            EventReference soundEvent = new EventReference();

            // For SoulCreatureTutorial, get sound2 directly
            var scTutorial = other.GetComponent<SoulCreatureTutorial>();
            if (scTutorial != null)
            {
                soundEvent = scTutorial.sound2;
            }
            else
            {
                // For all other cases, try to get sound2 from SoulCreatureAudio
                var scAudio = other.GetComponent<SoulCreatureAudio>();
                if (scAudio != null)
                {
                    soundEvent = scAudio.Sound2;
                }

                // --- NEW: Check if this soul creature disables backpack particles ---
                if (scAudio != null && scAudio.disableBackpackParticles)
                {
                    // Do not spawn any backpack particles for this creature
                    return;
                }
            }

            // Use the BackpackParticlesPool if available
            if (particlesPool != null)
            {
                // for BackpackParticlesHalo:
                if (boostEntry.backpackParticlesHaloInstance == null)
                {
                    boostEntry.backpackParticlesHaloInstance = particlesPool.GetBackpackHalo(soulColor, other);
                    if (boostEntry.backpackParticlesHaloInstance != null)
                    {
                        boostEntry.psHaloInstance = boostEntry.backpackParticlesHaloInstance.GetComponent<ParticleSystem>();
                    }
                }

                // for BackpackParticles:
                if (boostEntry.backpackParticlesInstance == null)
                {
                    boostEntry.backpackParticlesInstance = particlesPool.GetBackpackParticles(soulColor, soundEvent, other);
                    if (boostEntry.backpackParticlesInstance != null)
                    {
                        boostEntry.psInstance = boostEntry.backpackParticlesInstance.GetComponent<ParticleSystem>();
                    }
                }
            }
            // Fall back to the old instantiation method if pool is not available
            else
            {
                // for BackpackParticlesHalo:
                if (backpackParticlesHalo != null && boostEntry.backpackParticlesHaloInstance == null)
                {
                    boostEntry.backpackParticlesHaloInstance = Instantiate(backpackParticlesHalo, backpackParticlesHalo.transform.parent);
                    boostEntry.backpackParticlesHaloInstance.SetActive(true);
                    boostEntry.psHaloInstance = boostEntry.backpackParticlesHaloInstance.GetComponent<ParticleSystem>();

                    if (soulPs != null && boostEntry.psHaloInstance != null)
                    {
                        var mainHalo = boostEntry.psHaloInstance.main;
                        mainHalo.startColor = soulColor;
                    }
                }

                // for BackpackParticles:
                if (backpackParticles != null && boostEntry.backpackParticlesInstance == null)
                {
                    boostEntry.backpackParticlesInstance = Instantiate(backpackParticles, backpackParticles.transform.parent);
                    boostEntry.backpackParticlesInstance.SetActive(true);
                    boostEntry.psInstance = boostEntry.backpackParticlesInstance.GetComponent<ParticleSystem>();

                    // Add sound2 to the backpack particles
                    var soundOnDeath = boostEntry.backpackParticlesInstance.GetComponent<BackpackParticlesSoundOnDeath>();
                    if (soundOnDeath == null) soundOnDeath = boostEntry.backpackParticlesInstance.AddComponent<BackpackParticlesSoundOnDeath>();
                    soundOnDeath.sound2 = soundEvent;

                    if (soulPs != null && boostEntry.psInstance != null)
                    {
                        var mainParticles = boostEntry.psInstance.main;
                        mainParticles.startColor = soulColor;
                    }
                }
            }
        }

        // Increment flight boost per collision (for all soul creatures)
        float flightContributionChange = 0f;
        if (creatureBoostValue > 0f)
        {
            float neededToMaxFlight = boostEntry.maxPossibleFlightContribution - boostEntry.currentFlightContribution;
            flightContributionChange = Mathf.Min(0.8f, Mathf.Max(0, neededToMaxFlight));
            boostEntry.currentFlightContribution += flightContributionChange;
            currentTotalActualBoostTime += flightContributionChange;
        }

        // Increment speed boost per collision (for all soul creatures)
        float speedContributionChange = 0f;
        if (speedBoostValue > 0f)
        {
            float neededToMaxSpeed = boostEntry.maxPossibleSpeedContribution - boostEntry.currentSpeedContribution;
            speedContributionChange = Mathf.Min(0.8f, Mathf.Max(0, neededToMaxSpeed));
            boostEntry.currentSpeedContribution += speedContributionChange;
        }

        // Use the declared class member: maxTotalBoostCapacity
        currentTotalActualBoostTime = Mathf.Min(currentTotalActualBoostTime, maxTotalBoostCapacity);
        currentTotalActualBoostTime = Mathf.Max(0, currentTotalActualBoostTime);

        boostEntry.pendingDestruction = false;
        boostEntry.destructionTimer = 0f;
        boostEntry.haloPendingDestruction = false;
        boostEntry.haloDestructionTimer = 0f;

        if (boostEntry.psInstance != null)
        {
            var tempPsInstanceEmission = boostEntry.psInstance.emission;
            tempPsInstanceEmission.enabled = true;
        }
        if (boostEntry.psHaloInstance != null)
        {
            var tempPsHaloEmission = boostEntry.psHaloInstance.emission;
            tempPsHaloEmission.enabled = true;
        }

        // Also add this boost to the new flight boost system for visualization and debugging
        // This doesn't affect the actual boost amount, just provides visual feedback
        if (debugFlightBoosts)
        {
            // Get the color from the soul creature's particle system
            Color visualColor = Color.white;
            var soulPs = other.GetComponentInChildren<ParticleSystem>();
            if (soulPs != null)
            {
                visualColor = soulPs.main.startColor.color;
            }

            AddFlightTimeBoost(other, boostEntry.currentFlightContribution, visualColor, "SoulCreature");
        }
    }

    /// <summary>
    /// Handles the management of flight boosts including decay and cleanup
    /// </summary>
    private void HandleFlightBoosts()
    {
        if (flightBoostSources.Count == 0)
            return;

        // Clear and reuse existing list to avoid allocation
        reusableFlightBoostSourcesToRemove.Clear();

        // Check for null sources and apply decay
        foreach (var kvp in flightBoostSources)
        {
            GameObject source = kvp.Key;

            // Check for null sources
            if (source == null)
            {
                reusableFlightBoostSourcesToRemove.Add(source);
                continue;
            }

            // Check for destroyed sources
            if (!source.activeInHierarchy && source.scene.rootCount == 0)
            {
                reusableFlightBoostSourcesToRemove.Add(source);
                continue;
            }
        }

        // Remove any null or destroyed sources
        foreach (var source in reusableFlightBoostSourcesToRemove)
        {
            RemoveFlightTimeBoost(source);
        }
    }

    #region Flight Boost System

    /// <summary>
    /// Adds or updates a flight time boost from the specified source
    /// </summary>
    /// <param name="source">The GameObject providing the boost</param>
    /// <param name="amount">The amount of flight time to add</param>
    /// <returns>True if the boost was successfully added or updated</returns>
    public bool AddFlightTimeBoost(GameObject source, float amount)
    {
        return AddFlightTimeBoost(source, amount, Color.white, "");
    }

    /// <summary>
    /// Adds or updates a flight time boost with additional metadata
    /// </summary>
    /// <param name="source">The GameObject providing the boost</param>
    /// <param name="amount">The amount of flight time to add</param>
    /// <param name="color">Color for visual feedback</param>
    /// <param name="tag">Optional tag for categorizing the boost</param>
    /// <returns>True if the boost was successfully added or updated</returns>
    public bool AddFlightTimeBoost(GameObject source, float amount, Color color, string tag = "")
    {
        if (source == null)
        {
            if (debugFlightBoosts)
                Debug.LogWarning("Flight boost source cannot be null");
            return false;
        }

        // Enforce maximum boost sources limit
        if (!flightBoostSources.ContainsKey(source) && flightBoostSources.Count >= maxBoostSources)
        {
            if (debugFlightBoosts)
                Debug.LogWarning($"Maximum number of boost sources ({maxBoostSources}) reached. Cannot add new source.");
            return false;
        }

        // Add or update the boost source
        if (flightBoostSources.TryGetValue(source, out var existingBoost))
        {
            existingBoost.boostAmount = amount;
            existingBoost.boostColor = color;
            existingBoost.boostTag = tag;
            existingBoost.lastUpdateTime = Time.time;
            flightBoostSources[source] = existingBoost;

            if (debugFlightBoosts)
                Debug.Log($"Updated flight boost from {source.name}: {amount:F2}s");
        }
        else
        {
            flightBoostSources[source] = new FlightBoostSource
            {
                sourceObject = source,
                boostAmount = amount,
                boostColor = color,
                boostTag = tag,
                lastUpdateTime = Time.time
            };

            if (debugFlightBoosts)
                Debug.Log($"Added new flight boost from {source.name}: {amount:F2}s");
        }

        // Recalculate total boost
        RecalculateTotalBoost();
        return true;
    }

    /// <summary>
    /// Removes a flight time boost from the specified source
    /// </summary>
    /// <param name="source">The GameObject that provided the boost</param>
    /// <returns>True if the boost was successfully removed</returns>
    public bool RemoveFlightTimeBoost(GameObject source)
    {
        if (source == null || !flightBoostSources.ContainsKey(source))
            return false;

        // Clean up any visual feedback
        var boost = flightBoostSources[source];
        if (boost.visualFeedback != null)
            Destroy(boost.visualFeedback);

        flightBoostSources.Remove(source);

        if (debugFlightBoosts)
            Debug.Log($"Removed flight boost from {source.name}");

        // Recalculate total boost
        RecalculateTotalBoost();
        return true;
    }

    /// <summary>
    /// Gets the current flight boost amount from a specific source
    /// </summary>
    /// <param name="source">The source GameObject</param>
    /// <returns>The current boost amount, or 0 if the source is not found</returns>
    public float GetFlightBoostAmount(GameObject source)
    {
        if (source == null || !flightBoostSources.ContainsKey(source))
            return 0f;

        return flightBoostSources[source].boostAmount;
    }

    /// <summary>
    /// Gets all active flight boost sources
    /// </summary>
    /// <returns>A list of all active flight boost sources</returns>
    public List<FlightBoostSource> GetAllFlightBoosts()
    {
        return new List<FlightBoostSource>(flightBoostSources.Values);
    }

    /// <summary>
    /// Recalculates the total boost from all sources
    /// </summary>
    private void RecalculateTotalBoost()
    {
        float totalExternalBoost = 0f;
        foreach (var boost in flightBoostSources.Values)
        {
            totalExternalBoost += boost.boostAmount;
        }

        // Combine SoulCreature boosts with external boosts
        additionalMaxFlyTime = Mathf.Max(0f, currentTotalActualBoostTime + totalExternalBoost);

        // Speed boost is already calculated in HandleSoulCreatureBoosts
        // Just clamp to max speed boost
        currentSpeedBoost = Mathf.Min(currentSpeedBoost, maxSpeedBoost);
    }

    /// <summary>
    /// Clears all flight boosts
    /// </summary>
    public void ClearAllFlightBoosts()
    {
        // Clean up any visual feedback
        foreach (var boost in flightBoostSources.Values)
        {
            if (boost.visualFeedback != null)
                Destroy(boost.visualFeedback);
        }

        flightBoostSources.Clear();
        RecalculateTotalBoost();

        if (debugFlightBoosts)
            Debug.Log("Cleared all flight boosts");
    }

    // Legacy support for existing code
    [Obsolete("Use AddFlightTimeBoost instead")]
    public void UpdateExternalFlightBoost(GameObject source, float boostAmount)
    {
        AddFlightTimeBoost(source, boostAmount);
    }

    // Legacy support for existing code
    [Obsolete("Use RemoveFlightTimeBoost instead")]
    public void RemoveExternalFlightBoost(GameObject source)
    {
        RemoveFlightTimeBoost(source);
    }

    #endregion

    /// <summary>
    /// Toggles whether gravity affects the player in the downward direction.
    /// </summary>
    /// <param name="disable">When true, player is not affected by downward gravity</param>
    public void SetGravityDisabled(bool disable)
    {
        disableGravity = disable;
    }

}